package mobiarmy.ranking;

import mobiarmy.db.DBManager;
import mobiarmy.server.Glass;
import mobiarmy.server.User;
import mobiarmy.server.SessionManager;
import mobiarmy.server.Bot;
import mobiarmy.server.Exp;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;

/**
 * Class quản lý bảng xếp hạng
 */
public class RankingManager {
    
    private static final int PLAYERS_PER_PAGE = 10;
    
    /**
     * <PERSON><PERSON><PERSON> danh sách người chơi theo loại bảng xếp hạng
     */
    public static List<RankingPlayer> getRankingPlayers(RankingType type, int page) {
        List<RankingPlayer> players = new ArrayList<>();
        
        try {
            Connection connection = DBManager.getInstance().getConnection(DBManager.LOAD_CHAR);
            String sql = buildRankingQuery(type);
            
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setInt(1, page * PLAYERS_PER_PAGE);
            statement.setInt(2, PLAYERS_PER_PAGE);
            
            ResultSet rs = statement.executeQuery();
            int rank = page * PLAYERS_PER_PAGE + 1;
            
            while (rs.next()) {
                RankingPlayer player = new RankingPlayer();
                player.rank = rank++;
                player.id = rs.getInt("user_id");
                player.name = rs.getString("name");
                player.xu = rs.getLong("xu");
                player.luong = rs.getLong("luong");
                player.cup = rs.getInt("cup");
                player.clan = rs.getShort("clan");
                
                // Lấy thông tin level từ glasses JSON
                String glassesJson = rs.getString("glasses");
                byte selectedGlass = rs.getByte("selected_glass");
                player.level = parseGlassLevel(glassesJson, selectedGlass);
                player.levelPercent = parseGlassLevelPercent(glassesJson, selectedGlass);
                player.selectGlass = selectedGlass;
                
                player.equipIds = parseEquipIds(glassesJson, selectedGlass);
                
                // Tạo chuỗi hiển thị giá trị ranking
                player.displayValue = getRankingDisplayValue(type, player);
                
                players.add(player);
            }

            connection.close();
            rs.close();
            statement.close();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return players;
    }
    
    /**
     * Xây dựng câu query SQL theo loại bảng xếp hạng
     */
    private static String buildRankingQuery(RankingType type) {
        String baseQuery = "SELECT user_id, name, xu, luong, cup, clan, selected_glass, glasses FROM players WHERE 1=1";

        switch (type) {
            case DANH_DU:
                return baseQuery + " ORDER BY cup DESC LIMIT ?, ?";
            case CAO_THU:
                // Sắp xếp theo level cao nhất - tạm thời dùng cup để test, sau này sẽ cải thiện
                return baseQuery + " ORDER BY cup DESC LIMIT ?, ?";
            case DAI_GIA_XU:
                return baseQuery + " ORDER BY xu DESC LIMIT ?, ?";
            case DAI_GIA_LUONG:
                return baseQuery + " ORDER BY luong DESC LIMIT ?, ?";
            default:
                return baseQuery + " ORDER BY cup DESC LIMIT ?, ?";
        }
    }
    
    /**
     * Parse level từ glasses JSON
     */
    public static int parseGlassLevel(String glassesJson, byte selectedGlass) {
        try {
            if (glassesJson == null || glassesJson.isEmpty() || glassesJson.equals("[]")) {
                return 1;
            }

            JSONArray glassesArray = (JSONArray) JSONValue.parse(glassesJson);
            if (glassesArray != null) {
                for (int i = 0; i < glassesArray.size(); i++) {
                    JSONObject glassObj = (JSONObject) glassesArray.get(i);
                    byte glassID = Byte.parseByte(glassObj.get("glassID").toString());

                    if (glassID == selectedGlass) {
                        int exp = Integer.parseInt(glassObj.get("exp").toString());
                        return Exp.getLevelExp(exp);
                    }
                }
            }
            return 1;
        } catch (Exception e) {
            return 1;
        }
    }
    public static short[] parseEquipIds(String glassesJson, byte selectedGlass) {
        try {
            if (glassesJson == null || glassesJson.isEmpty() || glassesJson.equals("[]")) {
                return new short[]{0, 0, 0, 0, 0};
            }

            JSONArray glassesArray = (JSONArray) JSONValue.parse(glassesJson);
            if (glassesArray != null) {
                for (int i = 0; i < glassesArray.size(); i++) {
                    JSONObject glassObj = (JSONObject) glassesArray.get(i);
                    byte glassID = Byte.parseByte(glassObj.get("glassID").toString());

                    if (glassID == selectedGlass) {
                        JSONArray equipArray = (JSONArray) glassObj.get("equipID");
                        short[] equipIds = new short[equipArray.size()];
                        for (int j = 0; j < equipArray.size(); j++) {
                            equipIds[j] = Short.parseShort(equipArray.get(j).toString());
                        }
                        return equipIds;
                    }
                }
            }
            return new short[]{0, 0, 0, 0, 0};
        } catch (Exception e) {

        }
        return new short[]{0, 0, 0, 0, 0};
    }

    /**
     * Parse level percent từ glasses JSON
     */
    public static int parseGlassLevelPercent(String glassesJson, byte selectedGlass) {
        try {
            if (glassesJson == null || glassesJson.isEmpty() || glassesJson.equals("[]")) {
                return 0;
            }

            JSONArray glassesArray = (JSONArray) JSONValue.parse(glassesJson);
            if (glassesArray != null) {
                for (int i = 0; i < glassesArray.size(); i++) {
                    JSONObject glassObj = (JSONObject) glassesArray.get(i);
                    byte glassID = Byte.parseByte(glassObj.get("glassID").toString());

                    if (glassID == selectedGlass) {
                        int level = Integer.parseInt(glassObj.get("level").toString());
                        int exp = Integer.parseInt(glassObj.get("exp").toString());
                        return Exp.getPercentExp(level, exp);
                    }
                }
            }
            return 0;
        } catch (Exception e) {
            return 0;
        }
    }

    
    /**
     * Lấy giá trị hiển thị cho ranking
     */
    private static String getRankingDisplayValue(RankingType type, RankingPlayer player) {
        switch (type) {
            case DANH_DU:
                return String.valueOf(player.cup);
            case CAO_THU:
                return "Lv." + player.level;
            case DAI_GIA_XU:
                return formatNumber(player.xu) + " xu";
            case DAI_GIA_LUONG:
                return formatNumber(player.luong) + " lượng";
            default:
                return "";
        }
    }
    
    /**
     * Format số với đơn vị K, M
     */
    private static String formatNumber(long number) {
        if (number >= 1000000) {
            return (number / 1000000) + "M";
        } else if (number >= 1000) {
            return (number / 1000) + "K";
        }
        return String.valueOf(number);
    }
    
    /**
     * Class chứa thông tin người chơi trong bảng xếp hạng
     */
    public static class RankingPlayer {
        public int rank;
        public int id;
        public String name;
        public long xu;
        public long luong;
        public byte selectGlass;
        public int cup;
        public short clan;
        public int level;
        public int levelPercent;
        public short[] equipIds;
        public String displayValue;
    }
}
