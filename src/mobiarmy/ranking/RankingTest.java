package mobiarmy.ranking;

import mobiarmy.ranking.RankingManager.RankingPlayer;
import java.util.List;

/**
 * Class test tính năng bảng xếp hạng
 */
public class RankingTest {
    
    public static void testRankingTypes() {
        System.out.println("=== Test RankingType ===");
        
        // Test getAllDisplayNames
        String[] names = RankingType.getAllDisplayNames();
        System.out.println("Các loại bảng xếp hạng:");
        for (int i = 0; i < names.length; i++) {
            System.out.println(i + ": " + names[i]);
        }
        
        // Test getById
        for (int i = 0; i < 4; i++) {
            RankingType type = RankingType.getById(i);
            System.out.println("ID " + i + " -> " + type.getDisplayName() + " (order by: " + type.getOrderField() + ")");
        }
    }
    
    public static void testRankingManager() {
        System.out.println("\n=== Test RankingManager ===");
        
        try {
            // Test từng loại bảng xếp hạng
            for (RankingType type : RankingType.values()) {
                System.out.println("\n--- " + type.getDisplayName() + " ---");
                List<RankingPlayer> players = RankingManager.getRankingPlayers(type, 0);
                
                if (players.isEmpty()) {
                    System.out.println("Không có dữ liệu");
                } else {
                    System.out.println("Top " + players.size() + " người chơi:");
                    for (RankingPlayer player : players) {
                        System.out.println(String.format("#%d - %s (ID: %d) - %s", 
                            player.rank, player.name, player.id, player.displayValue));
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Lỗi test RankingManager: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void main(String[] args) {
        testRankingTypes();
        // testRankingManager(); // Uncomment khi có database connection
    }
}
