package mobiarmy;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import mobiarmy.server.Server;
import mobiarmy.server.ConfigManager;
import mobiarmy.server.SessionManager;
import mobiarmy.server.User;
import mobiarmy.db.DBManager;
import org.apache.log4j.PropertyConfigurator;

public class MobiArmy extends JFrame {

    private Server server;
    private JButton startButton;
    private JButton stopButton;
    private JButton restartButton;
    private JButton configButton;
    private JButton logButton;
    private JButton kickPlayerButton;
    private JButton restartDBButton;
    private JButton saveDataButton;
    private JLabel statusLabel;
    private JLabel connectionLabel;
    private JLabel uptimeLabel;
    private JLabel portLabel;
    private JTextArea logArea;
    private JScrollPane logScrollPane;
    private Thread serverThread;
    private Timer statusTimer;
    private long serverStartTime;
    private boolean autoStartEnabled = true;

    public MobiArmy() {
        // Thiết lập frame
        setTitle("MobiArmy Server Manager v2.0");
        setSize(800, 600);
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        setLocationRelativeTo(null);

        // Thiết lập icon cho ứng dụng
        try {
            setIconImage(Toolkit.getDefaultToolkit().getImage("res/icon/server.png"));
        } catch (Exception e) {
            // Ignore if icon not found
        }

        // Tạo giao diện
        createUI();

        // Thiết lập sự kiện đóng cửa sổ
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                onWindowClosing();
            }
        });

        // Tải cấu hình và khởi tạo server
        ConfigManager config = ConfigManager.getInstance();
        config.printAllConfig(); // In ra cấu hình để kiểm tra
        server = new Server(config.getServerPort());

        // Khởi động timer để cập nhật trạng thái
        startStatusTimer();

        // Tự động khởi động server nếu được bật
        if (autoStartEnabled) {
            SwingUtilities.invokeLater(() -> {
                addLogMessage("Auto-starting server...");
                startServer();
            });
        }
    }

    private void createUI() {
        setLayout(new BorderLayout());

        // Tạo panel chính
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // Panel thông tin server (phía trên)
        JPanel infoPanel = createInfoPanel();

        // Panel điều khiển (giữa)
        JPanel controlPanel = createControlPanel();

        // Panel log (phía dưới)
        JPanel logPanel = createLogPanel();

        // Thêm các panel vào main panel
        mainPanel.add(infoPanel, BorderLayout.NORTH);
        mainPanel.add(controlPanel, BorderLayout.CENTER);
        mainPanel.add(logPanel, BorderLayout.SOUTH);

        add(mainPanel, BorderLayout.CENTER);

        // Tạo menu bar
        createMenuBar();
    }

    private JPanel createInfoPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 2, 10, 5));
        panel.setBorder(new TitledBorder("Server Information"));

        // Khởi tạo các label
        statusLabel = new JLabel("Server status: Stopped");
        connectionLabel = new JLabel("Connected clients: 0");
        uptimeLabel = new JLabel("Uptime: 00:00:00");
        ConfigManager config = ConfigManager.getInstance();
        portLabel = new JLabel("Port: " + config.getServerPort());

        // Thiết lập màu sắc
        statusLabel.setForeground(Color.RED);
        connectionLabel.setForeground(Color.BLUE);
        uptimeLabel.setForeground(Color.DARK_GRAY);
        portLabel.setForeground(Color.DARK_GRAY);

        panel.add(statusLabel);
        panel.add(connectionLabel);
        panel.add(uptimeLabel);
        panel.add(portLabel);

        return panel;
    }

    private JPanel createControlPanel() {
        JPanel panel = new JPanel(new GridLayout(3, 3, 10, 10));
        panel.setBorder(new TitledBorder("Server Control"));

        // Khởi tạo các nút
        startButton = new JButton("Start Server");
        stopButton = new JButton("Stop Server");
        restartButton = new JButton("Restart Server");
        configButton = new JButton("Config");
        logButton = new JButton("View Logs");
        kickPlayerButton = new JButton("Kick Player");
        restartDBButton = new JButton("Restart DB");
        saveDataButton = new JButton("Save Data");
        JButton aboutButton = new JButton("About");

        // Thiết lập trạng thái ban đầu
        stopButton.setEnabled(false);
        restartButton.setEnabled(false);
        kickPlayerButton.setEnabled(false);
        saveDataButton.setEnabled(false);

        // Thiết lập màu sắc cho các nút
        startButton.setBackground(new Color(76, 175, 80));
        startButton.setForeground(Color.WHITE);
        stopButton.setBackground(new Color(244, 67, 54));
        stopButton.setForeground(Color.WHITE);
        restartButton.setBackground(new Color(255, 152, 0));
        restartButton.setForeground(Color.WHITE);
        kickPlayerButton.setBackground(new Color(156, 39, 176));
        kickPlayerButton.setForeground(Color.WHITE);
        restartDBButton.setBackground(new Color(63, 81, 181));
        restartDBButton.setForeground(Color.WHITE);
        saveDataButton.setBackground(new Color(0, 150, 136));
        saveDataButton.setForeground(Color.WHITE);

        // Thiết lập sự kiện
        startButton.addActionListener(e -> startServer());
        stopButton.addActionListener(e -> stopServer());
        restartButton.addActionListener(e -> restartServer());
        configButton.addActionListener(e -> showConfigDialog());
        logButton.addActionListener(e -> showLogDialog());
        kickPlayerButton.addActionListener(e -> showKickPlayerDialog());
        restartDBButton.addActionListener(e -> restartDBManager());
        saveDataButton.addActionListener(e -> saveAllData());
        aboutButton.addActionListener(e -> showAboutDialog());

        panel.add(startButton);
        panel.add(stopButton);
        panel.add(restartButton);
        panel.add(kickPlayerButton);
        panel.add(restartDBButton);
        panel.add(saveDataButton);
        panel.add(configButton);
        panel.add(logButton);
        panel.add(aboutButton);

        return panel;
    }

    private JPanel createLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new TitledBorder("Server Log"));

        logArea = new JTextArea(8, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        logArea.setBackground(Color.BLACK);
        logArea.setForeground(Color.GREEN);

        logScrollPane = new JScrollPane(logArea);
        logScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);

        panel.add(logScrollPane, BorderLayout.CENTER);

        // Thêm nút clear log
        JButton clearLogButton = new JButton("Clear Log");
        clearLogButton.addActionListener(e -> logArea.setText(""));
        panel.add(clearLogButton, BorderLayout.SOUTH);

        return panel;
    }

    private void createMenuBar() {
        JMenuBar menuBar = new JMenuBar();

        // Menu Server
        JMenu serverMenu = new JMenu("Server");
        JMenuItem autoStartItem = new JCheckBoxMenuItem("Auto Start", autoStartEnabled);
        autoStartItem.addActionListener(e -> {
            autoStartEnabled = ((JCheckBoxMenuItem) e.getSource()).isSelected();
            addLogMessage("Auto start " + (autoStartEnabled ? "enabled" : "disabled"));
        });

        JMenuItem exitItem = new JMenuItem("Exit");
        exitItem.addActionListener(e -> onWindowClosing());

        serverMenu.add(autoStartItem);
        serverMenu.addSeparator();
        serverMenu.add(exitItem);

        // Menu Help
        JMenu helpMenu = new JMenu("Help");
        JMenuItem aboutItem = new JMenuItem("About");
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);

        menuBar.add(serverMenu);
        menuBar.add(helpMenu);

        setJMenuBar(menuBar);
    }

    // Hàm khởi động server trong luồng riêng
    private void startServer() {
        if (server.isRunning()) {
            addLogMessage("Server is already running!");
            return;
        }

        addLogMessage("Starting server...");
        serverStartTime = System.currentTimeMillis();

        serverThread = new Thread(() -> {
            try {
                server.start();
            } catch (Exception ex) {
                ex.printStackTrace();
                SwingUtilities.invokeLater(() -> {
                    addLogMessage("Failed to start server: " + ex.getMessage());
                    updateServerStatus(false);
                });
            }
        });

        serverThread.start();
        updateServerStatus(true);
        addLogMessage("Server started successfully on port " + ConfigManager.getInstance().getServerPort());
    }

    // Hàm dừng server
    private void stopServer() {
        if (!server.isRunning()) {
            addLogMessage("Server is already stopped!");
            return;
        }

        addLogMessage("Stopping server...");
        server.stop();

        try {
            if (serverThread != null) {
                serverThread.join(5000); // Chờ tối đa 5 giây
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            addLogMessage("Error while stopping server: " + e.getMessage());
        }

        updateServerStatus(false);
        addLogMessage("Server stopped successfully");
    }

    private void restartServer() {
        addLogMessage("Restarting server...");
        stopServer();

        // Đợi một chút trước khi khởi động lại
        Timer restartTimer = new Timer();
        restartTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                SwingUtilities.invokeLater(() -> startServer());
            }
        }, 2000);
    }

    private void updateServerStatus(boolean isRunning) {
        SwingUtilities.invokeLater(() -> {
            if (isRunning) {
                statusLabel.setText("Server status: Running");
                statusLabel.setForeground(new Color(76, 175, 80));
                startButton.setEnabled(false);
                stopButton.setEnabled(true);
                restartButton.setEnabled(true);
                kickPlayerButton.setEnabled(true);
                saveDataButton.setEnabled(true);
            } else {
                statusLabel.setText("Server status: Stopped");
                statusLabel.setForeground(Color.RED);
                startButton.setEnabled(true);
                stopButton.setEnabled(false);
                restartButton.setEnabled(false);
                kickPlayerButton.setEnabled(false);
                saveDataButton.setEnabled(false);
                connectionLabel.setText("Connected clients: 0");
                uptimeLabel.setText("Uptime: 00:00:00");
            }
        });
    }

    private void startStatusTimer() {
        statusTimer = new Timer();
        statusTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                SwingUtilities.invokeLater(() -> updateStatus());
            }
        }, 1000, 1000); // Cập nhật mỗi giây
    }

    private void updateStatus() {
        if (server.isRunning()) {
            // Cập nhật số client kết nối
            int clientCount = SessionManager.getSessionsSize();
            connectionLabel.setText("Connected clients: " + clientCount);

            // Cập nhật uptime
            long uptime = System.currentTimeMillis() - serverStartTime;
            String uptimeStr = formatUptime(uptime);
            uptimeLabel.setText("Uptime: " + uptimeStr);
        }
    }

    private String formatUptime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        seconds = seconds % 60;

        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }

    private void addLogMessage(String message) {
        SwingUtilities.invokeLater(() -> {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            String timestamp = sdf.format(new Date());
            String logMessage = "[" + timestamp + "] " + message + "\n";

            logArea.append(logMessage);
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
    }

    private void showConfigDialog() {
        ConfigManager config = ConfigManager.getInstance();

        JDialog dialog = new JDialog(this, "Server Configuration", true);
        dialog.setSize(400, 300);
        dialog.setLocationRelativeTo(this);

        JPanel panel = new JPanel(new GridLayout(5, 2, 10, 10));
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Server Port
        panel.add(new JLabel("Server Port:"));
        JTextField portField = new JTextField(String.valueOf(config.getServerPort()));
        panel.add(portField);

        // Database URL
        panel.add(new JLabel("Database URL:"));
        JTextField dbUrlField = new JTextField(config.getDatabaseUrl());
        panel.add(dbUrlField);

        // Database Username
        panel.add(new JLabel("Database Username:"));
        JTextField dbUserField = new JTextField(config.getDatabaseUsername());
        panel.add(dbUserField);

        // Auto Generate Bot
        panel.add(new JLabel("Auto Generate Bot:"));
        JCheckBox autoBotCheck = new JCheckBox("", config.isAutoGenerateBot());
        panel.add(autoBotCheck);

        // Buttons
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton saveButton = new JButton("Save");
        JButton cancelButton = new JButton("Cancel");

        saveButton.addActionListener(e -> {
            addLogMessage("Configuration updated (restart required)");
            dialog.dispose();
        });

        cancelButton.addActionListener(e -> dialog.dispose());

        buttonPanel.add(saveButton);
        buttonPanel.add(cancelButton);

        dialog.add(panel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);
        dialog.setVisible(true);
    }

    private void showLogDialog() {
        JDialog dialog = new JDialog(this, "Server Logs", false);
        dialog.setSize(600, 400);
        dialog.setLocationRelativeTo(this);

        JTextArea logTextArea = new JTextArea();
        logTextArea.setEditable(false);
        logTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));

        // Đọc log từ file nếu có
        try {
            String logContent = new String(Files.readAllBytes(Paths.get("logs/army.log")));
            logTextArea.setText(logContent);
        } catch (IOException e) {
            logTextArea.setText("Could not read log file: " + e.getMessage());
        }

        JScrollPane scrollPane = new JScrollPane(logTextArea);
        dialog.add(scrollPane);
        dialog.setVisible(true);
    }

    private void showKickPlayerDialog() {
        JDialog dialog = new JDialog(this, "Kick Player", true);
        dialog.setSize(500, 400);
        dialog.setLocationRelativeTo(this);

        JPanel mainPanel = new JPanel(new BorderLayout());

        // Panel tìm kiếm
        JPanel searchPanel = new JPanel(new FlowLayout());
        searchPanel.setBorder(new TitledBorder("Search Player"));

        JTextField searchField = new JTextField(15);
        JButton searchButton = new JButton("Search");
        JRadioButton searchByName = new JRadioButton("By Name", true);
        JRadioButton searchById = new JRadioButton("By ID");

        ButtonGroup searchGroup = new ButtonGroup();
        searchGroup.add(searchByName);
        searchGroup.add(searchById);

        searchPanel.add(new JLabel("Search:"));
        searchPanel.add(searchField);
        searchPanel.add(searchButton);
        searchPanel.add(searchByName);
        searchPanel.add(searchById);

        // Danh sách player
        String[] columnNames = {"ID", "Name", "Level", "Status"};
        DefaultTableModel tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        JTable playerTable = new JTable(tableModel);
        playerTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        JScrollPane scrollPane = new JScrollPane(playerTable);
        scrollPane.setBorder(new TitledBorder("Online Players"));

        // Load danh sách player
        loadPlayerList(tableModel);

        // Panel nút
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton kickButton = new JButton("Kick Selected");
        JButton refreshButton = new JButton("Refresh");
        JButton closeButton = new JButton("Close");

        kickButton.setBackground(new Color(244, 67, 54));
        kickButton.setForeground(Color.WHITE);
        refreshButton.setBackground(new Color(33, 150, 243));
        refreshButton.setForeground(Color.WHITE);

        // Sự kiện
        searchButton.addActionListener(e -> {
            String searchText = searchField.getText().trim();
            if (!searchText.isEmpty()) {
                searchPlayer(tableModel, searchText, searchByName.isSelected());
            } else {
                loadPlayerList(tableModel);
            }
        });

        kickButton.addActionListener(e -> {
            int selectedRow = playerTable.getSelectedRow();
            if (selectedRow >= 0) {
                int playerId = (Integer) tableModel.getValueAt(selectedRow, 0);
                String playerName = (String) tableModel.getValueAt(selectedRow, 1);

                int confirm = JOptionPane.showConfirmDialog(
                    dialog,
                    "Are you sure you want to kick player: " + playerName + " (ID: " + playerId + ")?",
                    "Confirm Kick",
                    JOptionPane.YES_NO_OPTION
                );

                if (confirm == JOptionPane.YES_OPTION) {
                    kickPlayer(playerId, playerName);
                    loadPlayerList(tableModel);
                }
            } else {
                JOptionPane.showMessageDialog(dialog, "Please select a player to kick.", "No Selection", JOptionPane.WARNING_MESSAGE);
            }
        });

        refreshButton.addActionListener(e -> loadPlayerList(tableModel));
        closeButton.addActionListener(e -> dialog.dispose());

        buttonPanel.add(kickButton);
        buttonPanel.add(refreshButton);
        buttonPanel.add(closeButton);

        mainPanel.add(searchPanel, BorderLayout.NORTH);
        mainPanel.add(scrollPane, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        dialog.add(mainPanel);
        dialog.setVisible(true);
    }

    private void loadPlayerList(DefaultTableModel tableModel) {
        tableModel.setRowCount(0);

        try {
            ArrayList<User> users = SessionManager.generateUsers();
            for (User user : users) {
                if (user != null && user.session != null && user.session.connected) {
                    Object[] row = {
                        user.id,
                        user.name,
                        user.glass().level,
                        user.roomWait != null ? "In Room" : "Lobby"
                    };
                    tableModel.addRow(row);
                }
            }
            addLogMessage("Loaded " + tableModel.getRowCount() + " online players");
        } catch (Exception e) {
            addLogMessage("Error loading player list: " + e.getMessage());
        }
    }

    private void searchPlayer(DefaultTableModel tableModel, String searchText, boolean byName) {
        tableModel.setRowCount(0);

        try {
            ArrayList<User> users = SessionManager.generateUsers();
            for (User user : users) {
                if (user != null && user.session != null && user.session.connected) {
                    boolean matches = false;

                    if (byName) {
                        matches = user.name.toLowerCase().contains(searchText.toLowerCase());
                    } else {
                        try {
                            int searchId = Integer.parseInt(searchText);
                            matches = user.id == searchId;
                        } catch (NumberFormatException e) {
                            // Invalid ID format
                        }
                    }

                    if (matches) {
                        Object[] row = {
                            user.id,
                            user.name,
                            user.glass().level,
                            user.roomWait != null ? "In Room" : "Lobby"
                        };
                        tableModel.addRow(row);
                    }
                }
            }
            addLogMessage("Found " + tableModel.getRowCount() + " players matching: " + searchText);
        } catch (Exception e) {
            addLogMessage("Error searching players: " + e.getMessage());
        }
    }

    private void kickPlayer(int playerId, String playerName) {
        try {
            User user = SessionManager.findUserById(playerId);
            if (user != null && user.session != null) {
                user.session.requestDisconnect();
                addLogMessage("Kicked player: " + playerName + " (ID: " + playerId + ")");
                JOptionPane.showMessageDialog(this, "Player " + playerName + " has been kicked!", "Success", JOptionPane.INFORMATION_MESSAGE);
            } else {
                addLogMessage("Player not found or already disconnected: " + playerName);
                JOptionPane.showMessageDialog(this, "Player not found or already disconnected!", "Error", JOptionPane.ERROR_MESSAGE);
            }
        } catch (Exception e) {
            addLogMessage("Error kicking player " + playerName + ": " + e.getMessage());
            JOptionPane.showMessageDialog(this, "Error kicking player: " + e.getMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void restartDBManager() {
        if (!server.isRunning()) {
            JOptionPane.showMessageDialog(this, "Server must be running to restart DB Manager!", "Warning", JOptionPane.WARNING_MESSAGE);
            return;
        }

        int confirm = JOptionPane.showConfirmDialog(
            this,
            "This will restart the database connection pool.\nThis may cause temporary disconnections.\nContinue?",
            "Confirm DB Restart",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE
        );

        if (confirm == JOptionPane.YES_OPTION) {
            addLogMessage("Restarting Database Manager...");

            new Thread(() -> {
                try {
                    // Shutdown current DB connections
                    DBManager.getInstance().shutdown();
                    Thread.sleep(2000); // Wait 2 seconds

                    // Restart DB connections
                    DBManager.getInstance().start();

                    SwingUtilities.invokeLater(() -> {
                        addLogMessage("Database Manager restarted successfully");
                        JOptionPane.showMessageDialog(this, "Database Manager restarted successfully!", "Success", JOptionPane.INFORMATION_MESSAGE);
                    });
                } catch (Exception e) {
                    SwingUtilities.invokeLater(() -> {
                        addLogMessage("Error restarting DB Manager: " + e.getMessage());
                        JOptionPane.showMessageDialog(this, "Error restarting DB Manager: " + e.getMessage(), "Error", JOptionPane.ERROR_MESSAGE);
                    });
                }
            }).start();
        }
    }

    private void saveAllData() {
        if (!server.isRunning()) {
            JOptionPane.showMessageDialog(this, "Server must be running to save data!", "Warning", JOptionPane.WARNING_MESSAGE);
            return;
        }

        addLogMessage("Saving all player data...");

        new Thread(() -> {
            try {
                SessionManager.saveUsers();
                SwingUtilities.invokeLater(() -> {
                    addLogMessage("All player data saved successfully");
                    JOptionPane.showMessageDialog(this, "All player data saved successfully!", "Success", JOptionPane.INFORMATION_MESSAGE);
                });
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    addLogMessage("Error saving data: " + e.getMessage());
                    JOptionPane.showMessageDialog(this, "Error saving data: " + e.getMessage(), "Error", JOptionPane.ERROR_MESSAGE);
                });
            }
        }).start();
    }

    private void showAboutDialog() {
        String message = "MobiArmy Server Manager v2.1\n\n" +
                        "Enhanced server management panel with:\n" +
                        "• Auto-start functionality\n" +
                        "• Real-time status monitoring\n" +
                        "• Server restart capability\n" +
                        "• Player management (Kick)\n" +
                        "• Database restart\n" +
                        "• Data saving\n" +
                        "• Configuration management\n" +
                        "• Log viewing\n\n" +
                        "Developed for MobiArmy Game Server";

        JOptionPane.showMessageDialog(this, message, "About", JOptionPane.INFORMATION_MESSAGE);
    }

    private void onWindowClosing() {
        if (server.isRunning()) {
            int option = JOptionPane.showConfirmDialog(
                this,
                "Server is still running. Do you want to stop it before exiting?",
                "Confirm Exit",
                JOptionPane.YES_NO_CANCEL_OPTION
            );

            if (option == JOptionPane.YES_OPTION) {
                stopServer();
                // Đợi server dừng hoàn toàn
                Timer exitTimer = new Timer();
                exitTimer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        System.exit(0);
                    }
                }, 3000);
            } else if (option == JOptionPane.NO_OPTION) {
                System.exit(0);
            }
            // CANCEL_OPTION - không làm gì cả
        } else {
            System.exit(0);
        }
    }

    public static void main(String[] args) {
        // Tạo và hiển thị giao diện
        SwingUtilities.invokeLater(() -> {
            MobiArmy manager = new MobiArmy();
            manager.setVisible(true);
        });
    }
}
