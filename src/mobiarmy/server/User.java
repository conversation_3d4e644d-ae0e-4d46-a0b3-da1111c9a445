package mobiarmy.server;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;

import mobiarmy.util.Util;
import mobiarmy.db.DBManager;

import static mobiarmy.server.Text.__;

import mobiarmy.io.Message;
import mobiarmy.room.RoomInfo;
import mobiarmy.room.RoomWait;
import mobiarmy.util.Log;

/**
 *
 * <AUTHOR> Tú
 */
public class User {

    public Session session;
    public int id;
    public short clan;
    public String name;
    public String username;
    public String password;
    public Glass[] glass;
    public long xu;
    public long luong;
    public int cup;
    public byte selectGlass;
    public ArrayList<Item> items;
    public ArrayList<Equip> equips;
    public ArrayList<LinhTinh> linhtinhs;
    public ArrayList<Mission> missions;
    public ArrayList<Integer> friends;
    public ArrayList<Equip> generates;
    private Select select;
    private Confirm confirm;
    public RoomWait roomWait;
    public byte team;
    public boolean ready;
    public byte index;
    public byte[] setItems = new byte[] { 0, 0, 1, 1, -1, -1, -1, -1 };
    public boolean hasBeenInvited;
    public boolean lock;
    public long timeX2EXP;
    public Lock lockItem;

    public boolean isLoadFinish;
    public boolean isCleaned;
    private boolean saving;

    public User(String username, String password, Session session) {
        this.name = username;
        this.username = username;
        this.password = password;
        this.session = session;
    }
    public User(int id, String name) {
        this.id = id;
        this.name = name;
        this.session = null;
         this.createData();
    }

    public void login() {
        try {
            Pattern p = Pattern.compile("^[a-zA-Z0-9]+$");
            Matcher m1 = p.matcher(username);
            if (!m1.find()) {
                this.session.sessionHandler.log(__("Tên tài khoản có kí tự lạ."));
                return;
            }
            Connection connection = DBManager.getInstance().getConnection(DBManager.LOGIN);
            PreparedStatement statement = connection
                    .prepareStatement("SELECT id FROM user WHERE username = ? AND password = ?");
            statement.setString(1, username);
            statement.setString(2, password);
            ResultSet resultSet = statement.executeQuery();
            try {
                if (resultSet.next()) {
                    User user = SessionManager.findUserByName(username);
                    if (user != null) {
                        this.session.sessionHandler
                                .log(__("Tài khoản đang được đăng nhập trên máy khác hãy thử lại sau."));
                        if (user.session != null) {
                            user.session.sessionHandler.log(__("Tài khoản đang được đăng nhập trên máy khác."));
                            Util.setTimeout(()-> {
                                user.session.requestDisconnect();
                            }, 1500);
                        }
                        Util.setTimeout(()-> {
                            this.session.requestDisconnect();
                        }, 1500);
                        return;
                    }
                    this.id = resultSet.getInt("id");
                    SessionManager.addUser(this);
                    this.createData();
                    this.loadData();
                    this.isLoadFinish = true;
                    return;
                } else {
                    this.session.sessionHandler.log(__("Thông tin tài khoản hoặc mật khẩu không chính xác."));
                    return;
                }
            } finally {
                statement.close();
                connection.close();
            }
        } catch (Exception e) {
            Log.error("Error logging in user: " + e.getMessage());
        }
    }

    public void createData() {
        this.lock = false;
        this.glass = new Glass[10];
        this.equips = new ArrayList<>();
        this.missions = new ArrayList<>();
        this.friends = new ArrayList<>();
        this.linhtinhs = new ArrayList<>();
        for (int i = 0; i < this.glass.length; i++) {
            this.glass[i] = Glass.entrys[i].deepCopy();
            this.glass[i].user = User.this;
            this.glass[i].isOpen = this.glass[i].xu == 0 && this.glass[i].luong == 0;
            this.glass[i].updateAll();
        }
        this.items = new ArrayList<>();
        for (Item entry : Item.entrys) {
            entry = entry.deepCopy();
            if (entry.id < 2) {
                entry.num = 99;
            }
            this.items.add(entry);
        }
        for (Mission entry : Mission.entrys) {
            if (entry.level == 1) {
                entry = entry.deepCopy();
                entry.have = 0;
                this.missions.add(entry);
            }
        }
        this.xu = 1000;
        this.luong = 1000;
        this.lockItem = new ReentrantLock();
    }

    public void loadData() {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet rs = null;
        try {
            connection = DBManager.getInstance().getConnection(DBManager.LOAD_CHAR);
            statement = connection.prepareStatement("SELECT * FROM players WHERE user_id = ?");
            statement.setInt(1, this.id);
            rs = statement.executeQuery();
            try {
                if (rs.next()) {
                    this.xu = rs.getLong("xu");
                    this.luong = rs.getLong("luong");
                    this.cup = rs.getInt("cup");
                    this.clan = rs.getShort("clan");
                    this.selectGlass = rs.getByte("selected_glass");
                    String dateUser = rs.getString("data");
                    if (dateUser != null && !dateUser.isEmpty() && !dateUser.equals("[]")) {
                        this.setDataUser(dateUser);
                    }

                    // Parse JSON data for glasses
                    String glassesJson = rs.getString("glasses");
                    if (glassesJson != null && !glassesJson.isEmpty() && !glassesJson.equals("[]")) {
                        try {
                            JSONArray glassesArray = (JSONArray) JSONValue.parse(glassesJson);
                            if (glassesArray != null) {
                                for (int i = 0; i < glassesArray.size(); i++) {
                                    JSONObject glassObj = (JSONObject) glassesArray.get(i);

                                    byte glassID = Byte.parseByte(glassObj.get("glassID").toString());
                                    Glass glass = this.getGlass(glassID);
                                    if (glass != null) {
                                        glass.isOpen = true;

                                        // Parse ability array
                                        if (glassObj.get("ability") != null) {
                                            JSONArray abilityArray = (JSONArray) glassObj.get("ability");
                                            glass.ability = new int[abilityArray.size()];
                                            for (int j = 0; j < abilityArray.size(); j++) {
                                                glass.ability[j] = Integer.parseInt(abilityArray.get(j).toString());
                                            }
                                        }

                                        // Parse equipID array
                                        if (glassObj.get("equipID") != null) {
                                            JSONArray equipArray = (JSONArray) glassObj.get("equipID");
                                            glass.equipID = new short[equipArray.size()];
                                            for (int j = 0; j < equipArray.size(); j++) {
                                                glass.equipID[j] = Short.parseShort(equipArray.get(j).toString());
                                            }
                                        }

                                        // Handle data field
                                        String dataStr = glassObj.get("data") != null ? glassObj.get("data").toString()
                                                : null;
                                        glass.data = SessionManager.parseDataField(dataStr);

                                        glass.point = Short.parseShort(glassObj.get("point").toString());
                                        glass.level = Integer.parseInt(glassObj.get("level").toString());
                                        glass.exp = Integer.parseInt(glassObj.get("exp").toString());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            System.err
                                    .println("Error parsing glasses JSON for user " + this.id + ": " + e.getMessage());
                            e.printStackTrace();
                        }
                    }

                    // Parse JSON data for equips
                    String equipsJson = rs.getString("equips");
                    if (equipsJson != null && !equipsJson.isEmpty() && !equipsJson.equals("[]")) {
                        try {
                            this.equips.clear();
                            JSONArray equipsArray = (JSONArray) JSONValue.parse(equipsJson);
                            if (equipsArray != null) {
                                for (int i = 0; i < equipsArray.size(); i++) {
                                    JSONObject equipObj = (JSONObject) equipsArray.get(i);

                                    byte glassID = Byte.parseByte(equipObj.get("glassID").toString());
                                    short equipID = Short.parseShort(equipObj.get("equipID").toString());

                                    Equip equip = Equip.get(glassID, equipID).deepCopy();
                                    equip.level2 = Byte.parseByte(equipObj.get("level2").toString());

                                    // Handle Base64 encoded byte arrays
                                    if (equipObj.get("inv_ability") != null) {
                                        String dataStr = equipObj.get("inv_ability").toString();
                                        try {
                                            // Try to decode as Base64 first
                                            equip.inv_ability = Base64.getDecoder().decode(dataStr);
                                        } catch (IllegalArgumentException e) {
                                            // If Base64 decoding fails, treat as regular byte array
                                            try {
                                                JSONArray byteArray = (JSONArray) JSONValue.parse(dataStr);
                                                if (byteArray != null) {
                                                    equip.inv_ability = new byte[byteArray.size()];
                                                    for (int k = 0; k < byteArray.size(); k++) {
                                                        equip.inv_ability[k] = Byte
                                                                .parseByte(byteArray.get(k).toString());
                                                    }
                                                }
                                            } catch (Exception ex) {
                                                System.err.println("Error parsing inv_ability: " + ex.getMessage());
                                            }
                                        }
                                    }
                                    if (equipObj.get("inv_percen") != null) {
                                        String dataStr = equipObj.get("inv_percen").toString();
                                        try {
                                            // Try to decode as Base64 first
                                            equip.inv_percen = Base64.getDecoder().decode(dataStr);
                                        } catch (IllegalArgumentException e) {
                                            // If Base64 decoding fails, treat as regular byte array
                                            try {
                                                JSONArray byteArray = (JSONArray) JSONValue.parse(dataStr);
                                                if (byteArray != null) {
                                                    equip.inv_percen = new byte[byteArray.size()];
                                                    for (int k = 0; k < byteArray.size(); k++) {
                                                        equip.inv_percen[k] = Byte
                                                                .parseByte(byteArray.get(k).toString());
                                                    }
                                                }
                                            } catch (Exception ex) {
                                                System.err.println("Error parsing inv_percen: " + ex.getMessage());
                                            }
                                        }
                                    }

                                    // Parse slot array
                                    if (equipObj.get("slot") != null) {
                                        JSONArray slotArray = (JSONArray) equipObj.get("slot");
                                        equip.slot = new short[slotArray.size()];
                                        for (int j = 0; j < slotArray.size(); j++) {
                                            equip.slot[j] = Short.parseShort(slotArray.get(j).toString());
                                        }
                                    }

                                    equip.dbKey = Integer.parseInt(equipObj.get("dbKey").toString());
                                    equip.isUse = Integer.parseInt(equipObj.get("isUse").toString()) == 1;
                                    equip.renewalDate = Long.parseLong(equipObj.get("renewalDate").toString());

                                    this.equips.add(equip);
                                }
                            }
                        } catch (Exception e) {
                            System.err.println("Error parsing equips JSON for user " + this.id + ": " + e.getMessage());
                            e.printStackTrace();
                        }
                    }

                    // Parse JSON data for items
                    String itemsJson = rs.getString("items");
                    if (itemsJson != null && !itemsJson.isEmpty()) {
                        try {
                            this.items.clear();
                            JSONArray itemsArray = (JSONArray) JSONValue.parse(itemsJson);
                            if (itemsArray != null) {
                                for (int i = 0; i < itemsArray.size(); i++) {
                                    JSONObject itemObj = (JSONObject) itemsArray.get(i);

                                    byte itemId = Byte.parseByte(itemObj.get("item_id").toString());
                                    byte num = Byte.parseByte(itemObj.get("num").toString());

                                    Item item = Item.get(itemId).deepCopy();
                                    item.num = num;
                                    this.items.add(item);
                                }
                            }
                        } catch (Exception e) {
                            System.err.println("Error parsing items JSON for user " + this.id + ": " + e.getMessage());
                        }
                    }

                    // Parse JSON data for linhtinhs
                    String linhtinhsJson = rs.getString("linhtinhs");
                    if (linhtinhsJson != null && !linhtinhsJson.isEmpty()) {
                        try {
                            this.linhtinhs.clear();
                            JSONArray linhtinhsArray = (JSONArray) JSONValue.parse(linhtinhsJson);
                            if (linhtinhsArray != null) {
                                for (int i = 0; i < linhtinhsArray.size(); i++) {
                                    JSONObject linhTinhObj = (JSONObject) linhtinhsArray.get(i);

                                    int linhTinhId = Integer.parseInt(linhTinhObj.get("linhtinh_id").toString());
                                    int num = Integer.parseInt(linhTinhObj.get("num").toString());

                                    boolean isUse = false;
                                    if (linhTinhObj.get("isUse") != null) {
                                        isUse = Boolean.parseBoolean(linhTinhObj.get("isUse").toString());
                                    } else {
                                        isUse = false;
                                    }
                                    int level = 1;
                                    int exp = 1;
                                    int expMax = 100;
                                    if (linhTinhObj.get("level") != null) {
                                        level = Integer.parseInt(linhTinhObj.get("level").toString());
                                    }
                                    if (linhTinhObj.get("exp") != null) {
                                        exp = Integer.parseInt(linhTinhObj.get("exp").toString());
                                    }
                                    if (linhTinhObj.get("expMax") != null) {
                                        expMax = Integer.parseInt(linhTinhObj.get("expMax").toString());
                                    }
                                    short[] ability = null;
                                    if (linhTinhObj.get("ability") != null) {
                                        JSONArray abilityArray = (JSONArray) linhTinhObj.get("ability");
                                        ability = new short[abilityArray.size()];
                                        for (int j = 0; j < abilityArray.size(); j++) {
                                            ability[j] = Short.parseShort(abilityArray.get(j).toString());
                                        }
                                    }
                                    int[] skill = new int[] { -1, -1, -1, -1, -1 };
                                    if (linhTinhObj.get("skill") != null) {
                                        JSONArray skillArray = (JSONArray) linhTinhObj.get("skill");
                                        skill = new int[skillArray.size()];
                                        for (int j = 0; j < skillArray.size(); j++) {
                                            skill[j] = Integer.parseInt(skillArray.get(j).toString());
                                        }
                                    }
                                    LinhTinh item = LinhTinh.get(linhTinhId).deepCopy();
                                    item.num = num;
                                    item.isUse = isUse;
                                    item.level = level;
                                    item.exp = exp;
                                    item.expMax = expMax;
                                    if (ability != null)
                                        item.ability = ability;
                                    item.skill = skill;
                                    this.linhtinhs.add(item);
                                }
                            }
                        } catch (Exception e) {
                            System.err.println(
                                    "Error parsing linhtinhs JSON for user " + this.id + ": " + e.getMessage());
                        }
                    }

                    // Parse JSON data for missions
                    String missionsJson = rs.getString("missions");
                    if (missionsJson != null && !missionsJson.isEmpty() && !missionsJson.equals("[]")) {
                        try {
                            this.missions.clear();
                            JSONArray missionsArray = (JSONArray) JSONValue.parse(missionsJson);
                            if (missionsArray != null) {
                                for (int i = 0; i < missionsArray.size(); i++) {
                                    JSONObject missionObj = (JSONObject) missionsArray.get(i);

                                    byte missionId = Byte.parseByte(missionObj.get("mission_id").toString());
                                    byte level = Byte.parseByte(missionObj.get("level").toString());

                                    Mission mission = Mission.get(missionId, level);
                                    if (mission != null) {
                                        mission.have = Integer.parseInt(missionObj.get("have").toString());
                                        mission.isComplete = Integer
                                                .parseInt(missionObj.get("isComplete").toString()) == 1;
                                        mission.isGetReward = Integer
                                                .parseInt(missionObj.get("isGetReward").toString()) == 1;
                                        this.missions.add(mission);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            System.err
                                    .println("Error parsing missions JSON for user " + this.id + ": " + e.getMessage());
                            e.printStackTrace();
                        }
                    }

                    // Parse JSON data for friends
                    String friendsJson = rs.getString("friends");
                    if (friendsJson != null && !friendsJson.isEmpty()) {
                        try {
                            this.friends.clear();
                            JSONArray friendsArray = (JSONArray) JSONValue.parse(friendsJson);
                            if (friendsArray != null) {
                                for (int i = 0; i < friendsArray.size(); i++) {
                                    int friendId = Integer.parseInt(friendsArray.get(i).toString());
                                    this.friends.add(friendId);
                                }
                            }
                        } catch (Exception e) {
                            System.err
                                    .println("Error parsing friends JSON for user " + this.id + ": " + e.getMessage());
                        }
                    }
                }
            } finally {
                if (rs != null) {
                    try {
                        rs.close();
                    } catch (SQLException e) {
                        Log.error("Error closing ResultSet in loadData: " + e.getMessage());
                    }
                }
                if (statement != null) {
                    try {
                        statement.close();
                    } catch (SQLException e) {
                        Log.error("Error closing PreparedStatement in loadData: " + e.getMessage());
                    }
                }
                if (connection != null) {
                    try {
                        connection.close();
                    } catch (SQLException e) {
                        Log.error("Error closing Connection in loadData: " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            Log.error("Error in loadData for user " + this.name + ": " + e.getMessage(), e);
            if (this.session != null) {
                this.session.requestDisconnect();
            }
        }
    }

    public synchronized void saveData() {
        if (isLoadFinish && !saving) {
            try {
                saving = true;
                JSONArray glassesList = new JSONArray();
                for (Glass glass : this.glass) {
                    if (glass.isOpen) {
                        JSONObject glassObj = new JSONObject();
                        glassObj.put("glassID", glass.id);

                        // Convert ability array to JSONArray
                        if (glass.ability != null) {
                            JSONArray abilityArray = new JSONArray();
                            for (int ability : glass.ability) {
                                abilityArray.add(ability);
                            }
                            glassObj.put("ability", abilityArray);
                        }

                        // Convert equipID array to JSONArray
                        if (glass.equipID != null) {
                            JSONArray equipArray = new JSONArray();
                            for (short equipId : glass.equipID) {
                                equipArray.add(equipId);
                            }
                            glassObj.put("equipID", equipArray);
                        }

                        // Convert data array to JSON string or "null"
                        if (glass.data != null) {
                            JSONArray dataArray = new JSONArray();
                            for (short value : glass.data) {
                                dataArray.add(value);
                            }
                            glassObj.put("data", dataArray.toJSONString());
                        } else {
                            glassObj.put("data", "null");
                        }

                        glassObj.put("point", glass.point);
                        glassObj.put("level", glass.level);
                        glassObj.put("exp", glass.exp);
                        glassesList.add(glassObj);
                    }
                }

                // Prepare JSON data for equips
                JSONArray equipsList = new JSONArray();
                for (Equip equip : this.equips) {
                    JSONObject equipObj = new JSONObject();
                    equipObj.put("glassID", equip.glassID);
                    equipObj.put("equipID", equip.id);
                    equipObj.put("level2", equip.level2);

                    // Encode byte arrays as Base64
                    if (equip.inv_ability != null) {
                        JSONArray abilityArray = new JSONArray();
                        for (byte ability : equip.inv_ability) {
                            abilityArray.add(ability);
                        }
                        equipObj.put("inv_ability", abilityArray.toJSONString());
                    }
                    if (equip.inv_percen != null) {
                        JSONArray percentArray = new JSONArray();
                        for (byte percent : equip.inv_percen) {
                            percentArray.add(percent);
                        }
                        equipObj.put("inv_percen", percentArray.toJSONString());
                    }

                    // Convert slot array to JSONArray
                    if (equip.slot != null) {
                        JSONArray slotArray = new JSONArray();
                        for (short slot : equip.slot) {
                            slotArray.add(slot);
                        }
                        equipObj.put("slot", slotArray);
                    }

                    equipObj.put("dbKey", equip.dbKey);
                    equipObj.put("isUse", equip.isUse ? 1 : 0);
                    equipObj.put("renewalDate", equip.renewalDate);
                    equipsList.add(equipObj);
                }

                // Prepare JSON data for items
                JSONArray itemsList = new JSONArray();
                for (Item item : this.items) {
                    JSONObject itemObj = new JSONObject();
                    itemObj.put("item_id", item.id);
                    itemObj.put("num", item.num);
                    itemsList.add(itemObj);
                }

                // Prepare JSON data for linhtinhs
                JSONArray linhtinhsList = new JSONArray();
                for (LinhTinh linhtinh : this.linhtinhs) {
                    JSONObject linhTinhObj = new JSONObject();
                    linhTinhObj.put("linhtinh_id", linhtinh.id);
                    linhTinhObj.put("num", linhtinh.num);
                    linhTinhObj.put("isUse", linhtinh.isUse);
                    if (linhtinh.index == 0) {
                        linhTinhObj.put("level", linhtinh.level);
                        linhTinhObj.put("exp", linhtinh.exp);
                        linhTinhObj.put("expMax", linhtinh.expMax);
                        JSONArray abilityArray = new JSONArray();
                        for (int ability : linhtinh.ability) {
                            abilityArray.add(ability);
                        }
                        linhTinhObj.put("ability", abilityArray);
                        JSONArray skillArray = new JSONArray();
                        for (int skill : linhtinh.skill) {
                            skillArray.add(skill);
                        }
                        linhTinhObj.put("skill", skillArray);
                    }
                    linhtinhsList.add(linhTinhObj);
                }

                // Prepare JSON data for missions
                JSONArray missionsList = new JSONArray();
                for (Mission mission : this.missions) {
                    JSONObject missionObj = new JSONObject();
                    missionObj.put("mission_id", mission.id);
                    missionObj.put("level", mission.level);
                    missionObj.put("have", mission.have);
                    missionObj.put("isComplete", mission.isComplete ? 1 : 0);
                    missionObj.put("isGetReward", mission.isGetReward ? 1 : 0);
                    missionsList.add(missionObj);
                }
                JSONArray friendsArray = new JSONArray();
                for (Integer friendId : this.friends) {
                    friendsArray.add(friendId);
                }

                Connection conn = null;
                PreparedStatement checkStatement = null;
                PreparedStatement statement = null;
                ResultSet rs = null;

                try {
                    conn = DBManager.getInstance().getConnection(DBManager.SAVE_DATA);

                    // Check if player record exists
                    checkStatement = conn.prepareStatement("SELECT COUNT(*) FROM players WHERE user_id = ?");
                    checkStatement.setInt(1, this.id);
                    rs = checkStatement.executeQuery();
                    boolean exists = false;
                    if (rs.next()) {
                        exists = rs.getInt(1) > 0;
                    }
                    rs.close();
                    rs = null;
                    checkStatement.close();
                    checkStatement = null;
                    if (exists) {
                        // Update existing record
                        statement = conn.prepareStatement(
                                "UPDATE players SET xu = ?, luong = ?, cup = ?, clan = ?, selected_glass = ?, glasses = ?, equips = ?, items = ?, linhtinhs = ?, missions = ?, friends = ?, data = ?, updated_at = ?, name = ? WHERE user_id = ?");
                    } else {
                        // Insert new record
                        statement = conn.prepareStatement(
                                "INSERT INTO players (xu, luong, cup, clan, selected_glass, glasses, equips, items, linhtinhs, missions, friends, data, updated_at,name, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    }

                    statement.setLong(1, this.xu);
                    statement.setLong(2, this.luong);
                    statement.setInt(3, this.cup);
                    statement.setShort(4, this.clan);
                    statement.setByte(5, this.selectGlass);
                    statement.setString(6, glassesList.toJSONString());
                    statement.setString(7, equipsList.toJSONString());
                    statement.setString(8, itemsList.toJSONString());
                    statement.setString(9, linhtinhsList.toJSONString());
                    statement.setString(10, missionsList.toJSONString());
                    statement.setString(11, friendsArray.toJSONString());
                    statement.setString(12, this.getDataUser());
                    statement.setLong(13, System.currentTimeMillis() / 1000);
                    statement.setString(14, this.name);
                    statement.setInt(15, this.id);
                    statement.executeUpdate();
                } catch (Exception e) {
                    Log.error("Lỗi saveData User: " + this.name, e);
                } finally {
                    // Close resources in reverse order
                    if (rs != null) {
                        try {
                            rs.close();
                        } catch (SQLException e) {
                            Log.error("Error closing ResultSet in saveData: " + e.getMessage());
                        }
                    }
                    if (checkStatement != null) {
                        try {
                            checkStatement.close();
                        } catch (SQLException e) {
                            Log.error("Error closing checkStatement in saveData: " + e.getMessage());
                        }
                    }
                    if (statement != null) {
                        try {
                            statement.close();
                        } catch (SQLException e) {
                            Log.error("Error closing PreparedStatement in saveData: " + e.getMessage());
                        }
                    }
                    if (conn != null) {
                        try {
                            conn.close();
                        } catch (SQLException e) {
                            Log.error("Error closing Connection in saveData: " + e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                Log.error("Lỗi saveData User: " + this.name, e);
            } finally {
                saving = false;
            }
        }
    }

    public Glass glass() {
        return this.glass[this.selectGlass];
    }

    public void addXu(int add, boolean flag) {
        if (this.xu + add > Integer.MAX_VALUE) {
            this.xu = Integer.MAX_VALUE;
        } else {
            this.xu = this.xu + add;
        }
        if (flag) {
            this.session.sessionHandler.setXuLuong(this.xu, this.luong);
        }
    }

    public void addCup(int add, boolean flag) {
        if (this.cup + add > Integer.MAX_VALUE) {
            this.cup = Integer.MAX_VALUE;
        } else {
            this.cup = this.cup + add;
        }
        if (flag) {
            this.session.sessionHandler.addCup(this.cup, add);
        }
    }

    public void addLuong(int add, boolean flag) {
        if (this.luong + add > Integer.MAX_VALUE) {
            this.luong = Integer.MAX_VALUE;
        } else {
            this.luong = this.luong + add;
        }

        if (flag) {
            this.session.sessionHandler.setXuLuong(this.xu, this.luong);
        }
    }

    public Glass getGlass(byte glassID) {
        for (Glass g : this.glass) {
            if (g != null && g.id == glassID) {
                return g;
            }
        }
        return null;
    }

    public void buyGlass(int select, int type) {
        if (select >= 0 && select < this.glass.length && !this.glass[select].isOpen) {
            if (type == 0) {
                if (this.glass[select].xu <= this.xu) {
                    this.addXu(-this.glass[select].xu, true);
                    this.glass[select].isOpen = true;
                    this.session.sessionHandler.buyGlass(select - 3);
                }
            }
            if (type == 1) {
                if (this.glass[select].luong <= this.luong) {
                    this.addLuong(-this.glass[select].luong, true);
                    this.glass[select].isOpen = true;
                    this.session.sessionHandler.buyGlass(select - 3);
                }
            }
        }
    }

    public void selectGlass(byte select) {
        if (select >= 0 && select < this.glass.length && this.glass[select].isOpen) {
            this.selectGlass = select;
            this.glass().updateAll();
            if (this.session != null) {
                this.session.sessionHandler.selectGlass(this.id, this.selectGlass);
                this.session.sessionHandler.loadInfo();
                this.session.sessionHandler.loadDBKey();
            }
        }
    }

    public void addItem(int id, int num) {
        Item item = this.getItem(id);
        if (item != null) {
            item.num += num;
            if (item.num > 99) {
                item.num = 99;
            }
        } else {
            item = Item.get(id);
            if (item != null) {
                item = item.deepCopy();
                item.num = num;
                this.items.add(item);
            }
        }
    }

    public void addLinhTinh(int id, int add) {
        LinhTinh item = this.getLinhTinh(id);
        if (item == null) {
            if (add > 0) {
                item = LinhTinh.get(id).deepCopy();
                item.num = add;
                this.linhtinhs.add(item);
                if (this.session != null) {
                    this.session.sessionHandler.addLinhTinh(item, add);
                }
            }
        } else {
            item.num += add;
            if (item.num <= 0) {
                item.num = 0;
                this.linhtinhs.remove(item);
            }
            if (this.session != null) {
                if (add < 0) {
                    this.session.sessionHandler.downLinhTinh(id, -add);
                } else {
                    this.session.sessionHandler.addLinhTinh(item, add);
                }
            }
        }
    }

    public void useEquipPet(int id) {
        LinhTinh targetItem = getLinhTinh(id);
        if (targetItem == null) {
            session.sessionHandler.log(__("Vật phẩm không tồn tại"));
            return; // Item không tồn tại
        }

        // Kiểm tra index hợp lệ (0-3)
        if (targetItem.index < 0 || targetItem.index > 3) {
            session.sessionHandler.log(__("Không thể trang bị vật phẩm này"));
            return; // Index không hợp lệ
        }

        LinhTinh[] currentEquippedPet = this.getEquipPet();
        LinhTinh currentEquippedItem = null;
        for (LinhTinh item : currentEquippedPet) {
            if (item != null && item.id == id) {
                currentEquippedItem = item;
                break;
            }
        }

        if (currentEquippedItem != null) {
            // Item đang được trang bị, muốn tháo ra
            if (targetItem.index == 0) {
                // Muốn tháo index 0, kiểm tra xem còn item nào ở index 1,2,3 không
                boolean hasHigherIndexEquipped = false;
                for (int i = 1; i <= 3; i++) {
                    if (currentEquippedPet[i] != null) {
                        hasHigherIndexEquipped = true;
                        break;
                    }
                }

                if (hasHigherIndexEquipped) {
                    session.sessionHandler.log(__("Vui lòng tháo trang bị trước khi tháo pet"));
                    return; // Không thể tháo index 0 khi còn item ở index 1,2,3
                }
            }
            // Tháo item
            currentEquippedItem.isUse = false;
        } else {
            // Item chưa được trang bị, muốn trang bị

            // Kiểm tra quy tắc: phải có item index 0 trước khi trang bị index 1,2,3
            if (targetItem.index >= 1 && targetItem.index <= 3) {
                if (currentEquippedPet[0] == null) {
                    session.sessionHandler.log(__("Bạn phải trang bị pet trước khi trang bị phụ kiện"));
                    return; // Không thể trang bị index 1,2,3 khi chưa có index 0
                }
            }

            // Kiểm tra xem có item nào đang trang bị cùng index không
            LinhTinh sameIndexItem = currentEquippedPet[targetItem.index];

            if (sameIndexItem != null) {
                // Có item cùng index, thay thế
                sameIndexItem.isUse = false;
            }

            // Trang bị item mới
            targetItem.isUse = true;
        }
        this.session.sessionHandler.updateEquipPet();
        this.session.sessionHandler.reloadPet();
    }

    public LinhTinh[] getEquipPet() {
        LinhTinh[] equipPet = new LinhTinh[4]; // Mảng 4 phần tử cho index 0-3

        // Lọc các item đang được trang bị
        List<LinhTinh> equippedItems = this.linhtinhs.stream()
                .filter(linhTinh -> linhTinh.isUse)
                .collect(Collectors.toList());

        // Đặt từng item vào đúng vị trí theo index
        for (LinhTinh item : equippedItems) {
            if (item.index >= 0 && item.index <= 3) {
                equipPet[item.index] = item;
            }
        }

        return equipPet; // Trả về mảng với các vị trí trống là null
    }

    public void addEquip(int glassID, int id) {
        Equip equip = Equip.get((byte) glassID, (short) id).deepCopy();
        equip.dbKey = this.generateDBKey();
        equip.renewalDate = System.currentTimeMillis();
        this.equips.add(equip);
        this.session.sessionHandler.addEquip(equip);
    }

    public void addEquip(Equip equip) {
        equip.dbKey = this.generateDBKey();
        this.equips.add(equip);
        if (this.session != null) {
            this.session.sessionHandler.addEquip(equip);
        }
    }

    public void removeEquip(Equip equip) {
        this.equips.remove(equip);
        this.session.sessionHandler.removeEquip(equip);
    }

    public Equip getEquip(int dbKey) {
        for (Equip equip : this.equips) {
            if (equip.dbKey == dbKey) {
                return equip;
            }
        }
        return null;
    }

    public Equip getEquip(int id, int type) {
        for (Equip equip : this.equips) {
            if (equip.id == id && equip.type == type && equip.glassID == this.selectGlass && !equip.isUse) {
                return equip;
            }
        }
        return null;
    }

    public Equip getEquip(int id, int type, byte level) {
        for (Equip equip : this.equips) {
            if (equip.id == id && equip.type == type && equip.glassID == this.selectGlass && !equip.isUse
                    && equip.level2 == level) {
                return equip;
            }
        }
        return null;
    }

    public LinhTinh getLinhTinh(int id) {
        for (LinhTinh item : this.linhtinhs) {
            if (item.id == id) {
                return item;
            }
        }
        return null;
    }

    public void setEquipVip(byte action, int dbKey) {
        for (Equip equip : this.equips) {
            if (equip.glassID == this.glass().id && equip.vip != 0) {
                equip.isUse = false;
            }
        }
        Equip equip = this.getEquip(dbKey);
        if (equip != null && equip.date() > 0 && equip.vip != 0) {
            equip.isUse = action == 1;
        }
        this.glass().updateAll();
        if (this.session != null) {
            this.session.sessionHandler.setEquipItem(action, this.glass().data);
        }
    }

    public void setEquip(int[] dbKey) {
        for (int i = 0; i < dbKey.length; i++) {
            Equip equip = this.getEquip(dbKey[i]);
            if (equip != null && equip.date() > 0) {
                for (Equip e : this.equips) {
                    if (e.glassID == equip.glassID && e.type == equip.type) {
                        e.isUse = false;
                    }
                }
                equip.isUse = true;
            }
        }
        this.glass().updateAll();
        if (this.session != null) {
            this.session.sessionHandler.setEquip();
        }
    }

    public int generateDBKey() {
        int dbKey = this.equips.isEmpty() ? 1 : this.equips.get(this.equips.size() - 1).dbKey + 1;
        while (true) {
            boolean isDuplicate = false;
            for (Equip equip : this.equips) {
                if (equip.dbKey == dbKey) {
                    dbKey++;
                    isDuplicate = true;
                    break;
                }
            }
            if (!isDuplicate) {
                break; // Thoát khỏi vòng while khi không tìm thấy dbKey trùng
            }
        }
        return dbKey;
    }

    public Select getSelect() {
        if (this.select == null) {
            this.select = new Select(this);
        }
        return this.select;
    }

    public Confirm getConfirm() {
        if (this.confirm == null) {
            this.confirm = new Confirm(this);
        }
        return this.confirm;
    }

    public Item getItem(int id) {
        for (Item item : this.items) {
            if (item.id == id) {
                return item;
            }
        }
        return null;
    }

    public Glass getGlass(int id) {
        for (Glass g : this.glass) {
            if (g.id == id) {
                return g;
            }
        }
        return null;
    }

    public void buyEquip(int index, byte buyLuong) {
        if (this.generates != null && index >= 0 && index < this.generates.size()) {
            Equip equip = this.generates.get(index);
            int num = 1;
            int price = buyLuong == 1 ? equip.luong != -1 ? equip.luong * num : -1
                    : equip.xu != -1 ? equip.xu * num : -1;
            if ((buyLuong == 1 && price != -1 && price <= this.luong) || (price != -1 && price <= this.xu)) {
                if (buyLuong == 1) {
                    this.addLuong(-equip.luong, true);
                } else {
                    this.addXu(-equip.xu, true);
                }
                equip = equip.deepCopy();
                equip.renewalDate = System.currentTimeMillis();
                this.addEquip(equip);
                this.session.sessionHandler.log(__("Giao dịch thành công. Xin cảm ơn."));
            } else {
                this.session.sessionHandler.log(__("Bạn không đủ tiền để mua vật phẩm"));
            }
        }
    }

    public void buyItem(byte buyLuong, byte id, int num) {
        Item item = this.getItem(id);
        if (item != null && num > 0) {
            num = num + item.num > 99 ? 99 - item.num : num;
            if (num > 0) {
                int price = buyLuong == 1 ? item.luong != -1 ? item.luong * num : -1
                        : item.xu != -1 ? item.xu * num : -1;
                if ((buyLuong == 1 && price != -1 && price <= this.luong) || (price != -1 && price <= this.xu)) {
                    if (buyLuong == 1) {
                        this.addLuong(-price, false);
                    } else {
                        this.addXu(-price, false);
                    }
                    item.num += num;
                    this.session.sessionHandler.updateItem(item, (int) this.xu, (int) this.luong);
                } else {
                    this.session.sessionHandler.log(__("Bạn không đủ tiền để mua"));
                }
            }
        }
    }

    public void actionEquip(int dbKey[]) {
        int count = 0;
        int price = 0;
        for (int i = 0; i < dbKey.length; i++) {
            Equip equip = this.getEquip(dbKey[i]);
            if (dbKey.length == 1 && equip != null && !equip.isUse && equip.slot.length - equip.slot() > 0) {
                this.getConfirm().addConfirm2(1,
                        String.format(__("Bạn có muốn tháo hết ngọc trong trang bị này ra, chi phí %d xu"),
                                (int) (equip.priceSlot() * 0.25)),
                        equip.dbKey);
                return;
            }
            if (equip != null && !equip.isUse) {
                price += equip.calculatePrice();
                count++;
            }
        }
        if (count == 0) {
            this.session.sessionHandler.log(__("Không thể bán trang bị đang mặc"));
        } else {
            this.getConfirm().addConfirm2(2,
                    String.format(__("Bạn có muốn bán %d item này với giá %d xu không?"), count, price), dbKey);
        }
    }

    public void buyLinhTinh(byte buyLuong, byte id, byte num) {
        LinhTinh item = LinhTinh.get(id);
        if (item != null && num > 0) {
            int price = buyLuong == 1 ? item.luong != -1 ? item.luong * num : -1 : item.xu != -1 ? item.xu * num : -1;
            if ((buyLuong == 1 && price != -1 && price <= this.luong) || (price != -1 && price <= this.xu)) {
                if (buyLuong == 1) {
                    this.addLuong(-price, true);
                } else {
                    this.addXu(-price, true);
                }
                this.addLinhTinh(item.id, num);
                this.session.sessionHandler.log(__("Giao dịch thành công. Xin cảm ơn."));
            } else {
                this.session.sessionHandler.log(__("Bạn không đủ tiền để mua vật phẩm"));
            }
        }
    }

    public void renewalEquip(int dbKey) {
        Equip equip = this.session.user.getEquip(dbKey);
        if (equip != null) {
            if (this.session.user.xu >= equip.renewalPrice()) {
                this.session.user.addXu(-equip.renewalPrice(), true);
                equip.renewalDate = System.currentTimeMillis();
                this.session.sessionHandler.updateEquip(equip);
                this.session.user.session.sessionHandler.log(__("Gia hạn thành công."));
            } else {
                this.session.user.session.sessionHandler.log(__("Bạn không có đủ xu để gia hạn."));
            }
        }
    }

    public Mission getMission(int id) {
        for (Mission mission : this.missions) {
            if (mission.id == id) {
                return mission;
            }
        }
        return null;
    }

    public void addMission(int id, int add) {
        Mission mission = this.getMission(id);
        if (mission != null) {
            mission.have += add;
            if (mission.have >= mission.require) {
                mission.isComplete = true;
            }
        }
    }

    public void loadMission() {
        Mission[] array = new Mission[this.missions.size()];
        for (int i = 0; i < this.missions.size(); i++) {
            if (this.missions.get(i).isGetReward) {
                Mission mission = Mission.get(this.missions.get(i).id, this.missions.get(i).level + 1);
                if (mission != null) {
                    mission = mission.deepCopy();
                    int temp = this.missions.get(i).have;
                    this.missions.set(i, mission);
                    this.addMission(mission.id, temp);
                }
            }
            array[i] = this.missions.get(i);
        }
        this.session.sessionHandler.loadMission(array);
    }

    public void actionMission(int id) {
        Mission mission = this.getMission(id);
        if (mission.isComplete) {
            if (mission.isGetReward) {
                this.session.sessionHandler.log(__("Nhiệm vụ đã hoàn thành."));
            } else {
                mission.isGetReward = true;
                this.loadMission();
                if (mission.xu > 0) {
                    this.addXu(mission.xu, true);
                }
                if (mission.luong > 0) {
                    this.addLuong(mission.luong, true);
                }
                if (mission.exp > 0) {
                    this.glass().addExp(mission.exp, true);
                }
                if (mission.cup > 0) {
                    this.addCup(mission.cup, true);
                }
                this.session.sessionHandler
                        .log(String.format(__("Chúc mừng bạn nhận được phần thưởng %s!"), mission.reward));
            }
        } else {
            this.session.sessionHandler.log(__("Nhiệm vụ chưa hoàn thành."));
        }
    }

    public void loadFriend() {
        User[] users = new User[this.friends.size()];
        for (int i = 0; i < this.friends.size(); i++) {
            users[i] = SessionManager.findUserById(this.friends.get(i));
        }
        this.session.sessionHandler.loadFriend(users);
    }

    public void findUser(String name) {
        User user = SessionManager.findUserByName(name);
        if (user != null) {
            if (this.friends.contains(user.id)) {
                this.session.sessionHandler.log(__("Không thể thêm người này vì đã có sẵn."));
            } else {
                this.friends.add(user.id);
                this.session.sessionHandler.findUser(user.id, user.name);
                this.loadFriend();
            }
        } else {
            this.session.sessionHandler.log(__("Không tìm thấy nick bạn vừa nhập"));
        }
    }

    public void deleteFriend(int id) {
        this.friends.remove((Integer) id);
        this.session.sessionHandler.deleteFriend(id);
    }

    public void sendMessage(int id, String str) {
        User user = SessionManager.findUserById(id);
        if (user != null) {
            if (user.id == 2) {
                if (str.startsWith("adxp ")) {
                    int adxp = Integer.parseInt(str.split(" ")[1]);
                    this.glass().addExp(adxp, true);
                } else if (this.xu >= 10000) {
                    this.addXu(-10000, true);
                    SessionManager.messageWorld(String.format(__("Tin nhắn từ %s: %s"), this.name, str));
                }
            }
            if (user.session != null) {
                user.session.sessionHandler.messageTo(this.id, this.name, str);
            }
        }
    }

    public short[] getData() {
        if (this.glass().data != null) {
            return this.glass().data;
        }
        return this.glass().equipID;
    }

    public void loadRoomWaits(byte id) {
        RoomInfo roomInfo = RoomInfo.get(id);
        if (roomInfo != null) {
            RoomWait[] roomWaits = new RoomWait[roomInfo.roomWaits.size()];
            for (int i = 0; i < roomInfo.roomWaits.size(); i++) {
                roomWaits[i] = roomInfo.roomWaits.get(i);
            }
            if (this.session != null) {
                this.session.sessionHandler.loadRoomWaits(roomInfo.id, roomWaits);
            }
        }
    }

    public void joinRoomWait(byte roomID, byte boardID, String pass) {
        if (this.roomWait == null) {
            if (boardID == -1) {
                joinAnyBoard(roomID);
            } else {
                RoomWait wait = RoomInfo.get(roomID, boardID);
                if (wait != null) {
                    wait.join(this, pass);
                }
            }
        }

    }

    public void leaveRoomWait() {
        if (this.roomWait != null) {
            this.roomWait.leave(this);
        }
    }

    public void changeTeam() {
        if (this.roomWait != null) {
            this.roomWait.changeTeam(this);
        }
    }

    public void setItem(byte[] array) {
        if (!this.ready) {
            HashMap<Byte, Integer> itemCountMap = new HashMap<>();
            ArrayList<Byte> list = new ArrayList<>();

            for (int i = 0; i < this.setItems.length; i++) {
                if (array[i] < 0) {
                    list.add((byte) -1);
                    continue;
                }

                Item item = this.getItem(array[i]);

                int count = itemCountMap.getOrDefault(array[i], 0);
                if (item != null && count <= item.carryable && count <= item.num) {
                    list.add(array[i]);
                    itemCountMap.put(array[i], count + 1);
                }
            }

            for (int i = 0; i < list.size(); i++) {
                this.setItems[i] = list.get(i);
            }
        }
    }

    public byte getSlotItemError() {
        for (byte i = 0; i < this.setItems.length; i++) {
            if (this.setItems[i] != -1) {
                Item item = this.getItem(this.setItems[i]);
                if (item == null) {
                    return i;
                }
                // tui dung
                if (i == 4 && this.getItem(12) == null) {
                    return i;
                }
                if (i == 5 && this.getItem(13) == null) {
                    return i;
                }
                if (i == 6 && this.getItem(14) == null) {
                    return i;
                }
                if (i == 7 && this.getItem(15) == null) {
                    return i;
                }
            }
        }
        // this.session.service.log(String.format(this.session.mResources.startGameError4,this.name,
        // i));
        return -1;
    }

    public void ready() {
        if (this.roomWait != null) {
            this.roomWait.ready(this);
        }
    }

    public void addFriend(int userID) {
        User user = SessionManager.findUserById(userID);
        if (user == null) {
            if (this.session != null) {
                this.session.sessionHandler.addFriend((byte) 2);
            }
        } else if (this.friends.contains(user.id)) {
            if (this.session != null) {
                this.session.sessionHandler.addFriend((byte) 1);
            }
        } else {
            this.friends.add(user.id);
            if (this.session != null) {
                this.session.sessionHandler.addFriend((byte) 0);
            }
        }
    }

    public void kickRoomWait(int userID) {
        if (this.roomWait != null) {
            this.roomWait.kick(this, userID, __("Bạn bị kick khỏi bởi chủ phòng"));
        }
    }

    public void setPlayerLimitRoomWait(byte limit) {
        if (this.roomWait != null) {
            this.roomWait.setPlayerLimit(this, limit);
        }
    }

    public void changeMoneyRoomWait(int money) {
        if (this.roomWait != null) {
            this.roomWait.changeMoney(this, money);
        }
    }

    public void changePassRoomWait(String pass) {
        if (this.roomWait != null) {
            this.roomWait.changePass(this, pass);
        }
    }

    public void changeNameRoomWait(String name) {
        if (this.roomWait != null) {
            this.roomWait.changeName(this, name);
        }
    }

    public void changeMapRoomWait(byte mapID) {
        if (this.roomWait != null) {
            this.roomWait.changeMap(this, mapID);
        }
    }

    private void joinRandom() {
        ArrayList<RoomWait> roomWaits = new ArrayList<>();
        for (RoomInfo entry : RoomInfo.entrys) {
            for (RoomWait wait : entry.roomWaits) {
                if (!wait.started && wait.pass.isEmpty() && wait.money <= this.xu && wait.playerLimit > wait.numPlayer
                        && wait.numPlayer >= 1) {
                    roomWaits.add(wait);
                }
            }
        }
        if (roomWaits.isEmpty()) {
            if (this.session != null) {
                this.session.sessionHandler.log(__("Không tìm thấy khu vực yêu cầu!."));
            }
        } else {
            RoomWait wait = roomWaits.get(Util.nextInt(roomWaits.size()));
            this.joinRoomWait(wait.roomID, wait.boardID, wait.pass);
        }
    }

    private void joinEmpty() {
        for (RoomInfo entry : RoomInfo.entrys) {
            for (RoomWait wait : entry.roomWaits) {
                if (!wait.started && wait.pass.isEmpty() && wait.money <= this.xu && wait.playerLimit > wait.numPlayer
                        && wait.numPlayer == 0) {
                    this.joinRoomWait(wait.roomID, wait.boardID, wait.pass);
                    return;
                }
            }
        }
        if (this.session != null) {
            this.session.sessionHandler.log(__("Không tìm thấy khu vực yêu cầu!."));
        }
    }

    private void joinVS(byte vs) {
        for (RoomInfo entry : RoomInfo.entrys) {
            for (RoomWait wait : entry.roomWaits) {
                if (!wait.started && wait.pass.isEmpty() && wait.money <= this.xu && wait.playerLimit > wait.numPlayer
                        && wait.numPlayer >= 1 && wait.playerLimit == vs) {
                    this.joinRoomWait(wait.roomID, wait.boardID, wait.pass);
                    return;
                }
            }
        }
        // Không có phòng như yêu cầu sẽ cho bot vào thay thế
        for (RoomInfo entry : RoomInfo.entrys) {
            for (RoomWait wait : entry.roomWaits) {
                if (!wait.started && wait.pass.isEmpty() && wait.money <= this.xu && wait.playerLimit > wait.numPlayer
                        && wait.numPlayer == 0) {
                    // Chọn bản đồ ngẫu nhiên
                    wait.mapID = wait.maps.get(Util.nextInt(wait.maps.size()));
                    ArrayList<Bot> bots = new ArrayList<>(Bot.bots);
                    Collections.shuffle(bots);
                    ArrayList<User> players = new ArrayList<>();
                    players.add(this);
                    for (User bot : bots) {
                        if (players.size() >= vs) {
                            break;
                        }
                        if (bot.roomWait == null) {
                            players.add(bot);
                        }
                    }
                    // trộn lại
                    Collections.shuffle(players);
                    for (User user : players) {
                        user.joinRoomWait(wait.roomID, wait.boardID, wait.pass);
                    }
                    return;
                }
            }
        }

        if (this.session != null) {
            this.session.sessionHandler.log(__("Không tìm thấy khu vực yêu cầu!."));
        }
    }

    private void joinBoss() {
        for (RoomInfo entry : RoomInfo.entrys) {
            for (RoomWait wait : entry.roomWaits) {
                if (!wait.started && wait.pass.isEmpty() && wait.money <= this.xu && wait.playerLimit > wait.numPlayer
                        && wait.type == 5) {
                    this.joinRoomWait(wait.roomID, wait.boardID, wait.pass);
                    return;
                }
            }
        }
        if (this.session != null) {
            this.session.sessionHandler.log(__("Không tìm thấy khu vực yêu cầu!."));
        }
    }

    public void joinAnyBoard(byte type) {
        // khu ngau nhien
        if (type == -1) {
            this.joinRandom();
        }
        // khu trong
        if (type == 0) {
            this.joinEmpty();
        }
        // khu 1 vs 1
        if (type == 1) {
            this.joinVS((byte) 2);
        }
        // khu 2 vs 2
        if (type == 2) {
            this.joinVS((byte) 4);
        }
        // khu 3 vs 3
        if (type == 3) {
            this.joinVS((byte) 6);
        }
        // khu 4 vs 4
        if (type == 4) {
            this.joinVS((byte) 8);
        }
        // boss
        if (type == 5) {
            this.joinBoss();
        }
    }

    public void findPlayerToRoomWait(boolean find, int userID) {
        if (this.roomWait != null) {
            if (find) {
                if (this.session != null) {
                    int soluong = Util.nextInt(20);
                    ArrayList<User> players = new ArrayList<>();
                    ArrayList<User> users = SessionManager.generateUsers();
                    Collections.shuffle(users);
                    for (User user : users) {
                        if (players.size() >= soluong) {
                            break;
                        }
                        if (user.session != null && !user.hasBeenInvited && user.roomWait == null) {
                            players.add(user);
                        }
                    }
                    // Ít người chơi quá sẽ cho bot vào
                    ArrayList<Bot> bots = new ArrayList<>(Bot.bots);
                    Collections.shuffle(bots);
                    for (User bot : bots) {
                        if (players.size() >= soluong) {
                            break;
                        }
                        if (bot.roomWait == null) {
                            players.add(bot);
                        }
                    }
                    if (this.session != null) {
                        this.session.sessionHandler.tabInvite(players);
                    }
                }
            } else {
                if (userID < -1) {
                    this.roomWait.inviteBot(this, userID);
                } else {
                    this.roomWait.invitePlayer(this, userID);
                }
            }
        }
    }

    public void startGame() {
        if (this.roomWait != null) {
            this.roomWait.startGame(this);
        }
    }

    public void chat(String str) {
        if (this.roomWait != null) {
            this.roomWait.chat(this, str);
        }
    }

    public void moveLocation(short x, short y) {
        if (this.roomWait != null && this.roomWait.started) {
            this.roomWait.mapData.moveLocation(this.index, x, y);
        }
    }

    public void setXY(short x, short y) {
        if (this.roomWait != null && this.roomWait.started) {
            this.roomWait.mapData.setXY(this, x, y);
        }
    }

    public void skipTurn() {
        if (this.roomWait != null && this.roomWait.started) {
            this.roomWait.mapData.skipTurn(this.index);
        }
    }

    public void shoot(byte bulletId, short x, short y, short ang, byte force, byte force2, byte nshoot) {
        if (this.roomWait != null && this.roomWait.started) {
            if (force > 30) {
                force = 30;
            }
            if (force < 1) {
                force = 1;
            }
            if (force2 > 30) {
                force2 = 30;
            }
            if (force2 < 1) {
                force2 = 1;
            }
            nshoot = 1;
            this.roomWait.mapData.shoot(this.index, -1, ang, force, force2);
        }
    }

    public void useItem(byte itemID) {
        if (this.roomWait != null && this.roomWait.started) {
            this.roomWait.mapData.useItem(this.index, itemID);
        }
    }

    public void update() {
        if (this.roomWait != null && this.roomWait.training) {
            this.roomWait.update();
        }
    }

    public void startStraining(Message msg) {
        try {
            if (roomWait == null || !roomWait.training) {
                return;
            }
            byte type = msg.reader().readByte();
            if (type == 0) {
                this.startGame();
            } else if (type == 1) {
                this.leaveRoomWait();
                this.session.sessionHandler.leaveTraining();
            }
        } catch (IOException e) {

        }
    }

    public void joinTraining() {
        RoomWait roomWait = new RoomWait((byte) 0, (byte) 0, (byte) 1, new byte[] { 0 }, 0, 0, (byte) 2);
        roomWait.training = true;
        this.roomWait = roomWait;
        roomWait.players[0] = this;
        this.index = 0;
        this.ready = true;
        this.team = 1;
        roomWait.userID = this.id;
        Bot bot = Bot.botTraining(this);
        roomWait.players[1] = bot;
        bot.roomWait = roomWait;
        bot.index = 1;
        bot.ready = true;
        bot.team = 2;
        roomWait.noWaitTime = System.currentTimeMillis();
        roomWait.numPlayer = 2;
        this.session.sessionHandler.sendMapTraining();
    }

    public String getDataUser() {
        JSONObject json = new JSONObject();
        json.put("timeX2EXP", this.timeX2EXP);
        return json.toString();
    }

    public void setDataUser(String data) {
        JSONObject json = (JSONObject) JSONValue.parse(data);
        if (json.containsKey("timeX2EXP")) {
            this.timeX2EXP = (long) json.get("timeX2EXP");
        }
    }

    public void createItem(Message msg) {
        try {
            byte formulaId = msg.reader().readByte();
            byte action = msg.reader().readByte();
            byte level = 0;

            if (action == 2) {
                level = msg.reader().readByte();
            }

            System.out.println("CreateItem - FormulaId: " + formulaId + ", Action: " + action + ", Level: " + level);

            switch (action) {
                case 1: // Lấy thông tin formula
                    this.session.sessionHandler.sendFormulaList(formulaId);
                    break;

                case 2: // Chế tạo item
                    this.processCreateItem(formulaId, level);
                    break;

                default:
                    System.out.println("Unknown formula action: " + action);
                    break;
            }
        } catch (Exception e) {
            System.err.println("Error in createItem: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Xử lý việc chế tạo item từ formula
     */
    private void processCreateItem(byte formulaId, byte level) {
        try {
            level = (byte) (level + 1);
            Formula formula = Formula.get(formulaId, level);
            if (formula == null) {
                this.session.sessionHandler.log("Không tìm thấy công thức chế tạo");
                return;
            }

            // Kiểm tra level yêu cầu
            if (!formula.hasRequiredLevel(this)) {
                this.session.sessionHandler.sendFormulaNotification("Bạn chưa đủ level để sử dụng công thức này");
                return;
            }

            // Kiểm tra nguyên liệu
            if (!formula.hasRequiredMaterials(this)) {
                this.session.sessionHandler.sendFormulaNotification("Bạn không đủ nguyên liệu để chế tạo");
                return;
            }

            // Kiểm tra equipment cần thiết
            if (!formula.hasRequiredEquipment(this)) {
                this.session.sessionHandler.sendFormulaNotification("Bạn không có trang bị cần thiết");
                return;
            }

            // Trừ nguyên liệu
            if (!formula.consumeMaterials(this)) {
                this.session.sessionHandler.sendFormulaNotification("Bạn không đủ nguyên liệu để chế tạo");
                return;
            }

            // Tạo equipment mới với random stats
            this.session.sessionHandler.updateRuong();
            this.createEquipmentFromFormula(formula, level);

            this.session.sessionHandler.sendFormulaNotification("Chế tạo thành công!");

        } catch (Exception e) {
            System.err.println("Error in processCreateItem: " + e.getMessage());
            e.printStackTrace();
            this.session.sessionHandler.sendFormulaNotification("Có lỗi xảy ra khi chế tạo");
        }
    }

    /**
     * Tạo equipment từ formula với random stats
     */
    private void createEquipmentFromFormula(Formula formula, byte level) {
        try {
            if (formula.equipId == null || formula.equipId.length == 0) {
                System.err.println("Formula has no equipId defined");
                return;
            }

            // Lấy equipId dựa trên glass index
            short equipId = formula.equipId[this.selectGlass];

            // Random inv_ability và inv_percen
            byte[] invAbility = formula.randomInvAbility();
            byte[] invPercen = formula.randomInvPercen();

            // Tạo equipment mới
            Equip newEquip = Equip.get(this.selectGlass, equipId, formula.equipType).deepCopy();
            newEquip.inv_ability = invAbility;
            newEquip.inv_percen = invPercen;
            newEquip.level2 = level;
            newEquip.renewalDate = System.currentTimeMillis();
            addEquip(newEquip);

            System.out.println("Created equipment - ID: " + equipId + ", Level: " + level +
                    ", InvAbility: " + java.util.Arrays.toString(invAbility) +
                    ", InvPercen: " + java.util.Arrays.toString(invPercen));

        } catch (Exception e) {
            System.err.println("Error creating equipment from formula: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void cleanUp() {
        this.isCleaned = true;
        this.session = null;
        this.glass = null;
        this.equips = null;
        this.items = null;
        this.linhtinhs = null;
        this.missions = null;
        this.friends = null;
        this.generates = null;
        this.select = null;
        this.confirm = null;
        this.roomWait = null;
        this.setItems = null;
        this.lockItem = null;
        Log.debug("Cleaned up user: " + this.name);
    }
}
